#encoding:gbk
import os # --- ����������osģ���Դ����ļ�·���Ͳ���
import json # --- ����json��飬���ڴ���JSON��ʽ���ź��ļ�

# ������Ĺ�Ʊ���ͣ��ɶ�ѡ
allowed_stock_types = ["30", "688", "00", "60"]  # ��������Ĺ�Ʊ����
allow_add_position = True # �Ƿ����������гֲֵĹ�Ʊ����׷�Ӳ�λ��FalseΪ��ֹ  False


# �����˻�ID
account_id = "************"

# ���ײ���
max_position = 30  # ���ֲֹ�Ʊ����
buy_type = "fixed_amount"  # ��������: "fixed_amount", "total_fund_percent", "available_fund_percent", "lots_to_buy"
fixed_buy_amount = 40000  # �̶������Ԫ��
fund_percent = 0.02  # �ʽ�ٷֱȣ�2%��
available_fund_percent = 0.5  # �����ʽ�ٷֱȣ�50%��
lots_to_buy = 1  # Ĭ������������1��=100�ɣ�

# ���ȹ����Ʊ�б�
priority_stocks = ["600519.SH", "000001.SZ"]  # ʾ��������ę́��ƽ������

# �������Ʊ�б�����������
blacklist_stocks = ["300926.SZ", "002007.SZ"]  # ʾ����C�дɡ���������
# �����Ʊ�����а�����������һ�����ַ�����������
disallowed_name_substrings = ["N", "ST", "��"] # Ĭ�Ϲ����¹ɡ����վ�ʾ�ɺ�����������
# --- �������� ---


# ���Խ��׶���
buy_stocks = ["003040.SZ", "600000.SH", "600519.SH", "000001.SZ"]
sell_stocks = ["002313.SZ", "000078.SZ"]
trade_test_switch = 0  # ���ײ��Կ��أ�1Ϊ������0Ϊ�ر�


current_positions_file = "D:/current_positions.txt"

# --JSON�ļ��źŽ������� ---
signal_switch = 1  # �źŽ����ܿ��أ�1Ϊ������0Ϊ�ر�
# ͳһ�ź��ļ�·����ȡ��ԭ��EBK�ļ�
signal_file = "D:/gemini_signal.json"  # ͳһ�ź��ļ�·��
# --- ����������JSON�ļ��źŽ������� ---


# ��¼������������ԵĹ�Ʊ
today_bought = {}

# ��¼��ǰ�ź���ϸ��Ϣ
current_signals_detail = {}

# ==================== �����������뽫�˺������ӵ����Ľű��� ====================
def calculate_buy_lots(symbol, current_price, context, detail):
    """
    ����ȫ�����ü���Ӧ����Ĺ���(lots)��
    �˺�������֧������buy_type���������ƴ�����С��������
    ����: �����������������Ӧ�������������0��ʾ����
    """
    # ��ǰ��ȡ�˻��ʽ���Ϣ
    accouts = detail.get("accouts", {})
    total_money = accouts.get("all_money", 0)
    available_cash = accouts.get("Available_money", 0)
    
    # ȷ����С���뵥λ���ƴ���Ϊ200�ɣ�����Ϊ100�ɣ�
    min_lots_unit = 200 if symbol.startswith("688") else 100
    min_lots_in_100 = 2 if symbol.startswith("688") else 1 # ��100��Ϊ��λ����С����
    
    lots = 0
    calculated_amount = 0 # ���ڴ�ӡ��־�ļ�����
    
    # ���� buy_type ����
    if buy_type == "fixed_amount":
        calculated_amount = fixed_buy_amount
        if current_price > 0:
            lots = int(fixed_buy_amount / (current_price * 100)) * 100
            
    elif buy_type == "total_fund_percent":
        calculated_amount = total_money * fund_percent
        if current_price > 0:
            lots = int(calculated_amount / (current_price * 100)) * 100
            
    elif buy_type == "available_fund_percent":
        calculated_amount = available_cash * available_fund_percent
        if current_price > 0:
            lots = int(calculated_amount / (current_price * 100)) * 100
            
    elif buy_type == "lots_to_buy":
        lots = lots_to_buy * 100
        calculated_amount = lots * current_price

    # ȷ����������С���뵥λ
    if 0 < lots < min_lots_unit:
        print(f"    - ��ʾ: �������({lots})������С��λ({min_lots_unit})������Ϊ��С��λ��")
        lots = min_lots_unit
    elif lots == 0 and calculated_amount > 0:
         print(f"    - ��ʾ: ���ݽ�� {calculated_amount:.2f}Ԫ �͹ɼ� {current_price:.2f}Ԫ ������Ĺ�������1�֣��޷����롣")
         return 0

    # ����ǿƴ��壬ȷ����100��������
    if symbol.startswith("688") and lots > 0:
        lots = int(lots / 100) * 100

    # ������ռ�����Ϊ0��ֱ�ӷ���
    if lots <= 0:
        return 0

    return int(lots)
    
def print_buy_signal_details(stock_list, context):
    """
    ��ȡ����ӡ�����ź��б�����ϸ��Ϣ�����롢���ơ���ǰ�ǵ�������
    """
    print("--- ��ӡ�����ź���ϸ��Ϣ ---")
    if not stock_list:
        print("�����������źš�")
        return
    
    for code in stock_list:
        try:
            # ��׼������
            std_code = standardize_stock_code(code)
            if not std_code:
                print(f"�ź�����: ��Ʊ���� '{code}' ��Ч����������")
                continue

            # ��ȡ��Ʊ����
            name = context.get_stock_name(std_code)
            if not name: name = "δ֪"
            
            # �������еĺ�����ȡ�۸���ǵ���
            current_price, change_percent = get_current_price(std_code, context)
            
            if current_price <= 0:
                print(f"�ź�����: {std_code}({name}) - ��ȡ����ʧ�ܡ�")
            else:
                print(f"�ź�����: {std_code}({name}) - ��ǰ�Ƿ�: {change_percent:.2f}%")
        except Exception as e:
            print(f"��ȡ {code} ��ϸ��Ϣʱ����: {e}")
    print("--------------------------")
    
# --- ���ع�����ȡ�ź��ļ�������ɾ���ĺ��� ---
def read_and_delete_signal_file(file_path):
    """
    (�ɿ���) ��ȡ�ź��ļ������ع�Ʊ�����б���������ɾ���ļ���
    ɾ������ȷ����ÿ���ź��ļ�ֻ������һ�Σ��������ظ����׺��ļ���ͻ��
    """
    stock_codes = []
    if not os.path.exists(file_path):
        return stock_codes  # �ļ������ڣ�ֱ�ӷ��ؿ��б�

    try:
        # �����ļ������������ڵ��̵߳�iQuant�����в��Ǳ��룬�����Ǹ���ϰ��
        # ���ȣ���ȡ�ļ��������ݵ��ڴ�
        with open(file_path, 'r', encoding='gbk') as f: # ע�⣺iQuant������֮ǰ��д����뱣��һ��
            stock_codes = [line.strip() for line in f if line.strip() and len(line.strip()) >= 6]
        
        # �ɹ���ȡ������ɾ���ļ�
        if stock_codes:
            os.remove(file_path)
            print(f"��Ϣ - �Ѷ�ȡ��ɾ���ź��ļ�: {file_path}, �ź���: {len(stock_codes)}")
        else:
            # ����ļ��ǿյĻ�������Ч��Ҳɾ��������ֹ����
            os.remove(file_path)
            print(f"��Ϣ - �ź��ļ� {file_path} Ϊ�ջ���Ч����ɾ����")

    except Exception as e:
        print(f"���� - ��ȡ��ɾ���ź��ļ� '{file_path}' ʧ��: {e}")
        # ��������쳣�������ٴ�ɾ�����Է��Ƕ�ȡʱ�������ļ��Դ���
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"��Ϣ - ���쳣�����гɹ�ɾ���ļ�: {file_path}")
            except Exception as e_del:
                print(f"���� - �������쳣��ɾ���ļ� '{file_path}' �ٴ�ʧ��: {e_del}")
                
    return stock_codes


def read_and_delete_signal_file_new(file_path):
    """
    ���ܣ���ȡ�ź��ļ������ع�Ʊ�����б���������ɾ���ļ���
    ֧��JSON��EBK��������ʽ���ź��ļ�
    ɾ������ȷ����ÿ���ź��ļ�ֻ������һ�Σ��������ظ����׺��ļ���ͻ��

    :param file_path: �ź��ļ�·��
    :return: �ź��б� (JSON��ʽ����ź��ֵ��б�)
    """
    signals = []
    if not os.path.exists(file_path):
        return signals  # �ļ������ڣ�ֱ�ӷ��ؿ��б�

    try:
        # �ж��ļ�����
        is_json_file = file_path.lower().endswith('.json')

        if is_json_file:
            # ����JSON�ļ�
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # ��ȡsignals�б�
            if isinstance(data, dict) and 'signals' in data:
                signals = data['signals']
                print(f"��Ϣ - �Ѷ�ȡJSON�ź��ļ�: {file_path}, �ź���: {len(signals)}")
            else:
                print(f"���� - JSON�ļ���ʽ����: {file_path}")

        else:
            # ����EBK�ļ�(����ԭ�߼�)
            with open(file_path, 'r', encoding='gbk') as f:
                stock_codes = [line.strip() for line in f if line.strip() and len(line.strip()) >= 6]

            # ��EBK�����ת��Ϊ�ź��ֵ��ʽ
            signals = []
            for code in stock_codes:
                signal = {
                    "code": code,
                    "type": "buy",  # EBK�ļ�Ĭ��Ϊ������
                    "level": "MBG",
                    "reason": "EBK�ļ��ź�",
                    "plan_amount": 50000
                }
                signals.append(signal)

            if signals:
                print(f"��Ϣ - �Ѷ�ȡEBK�ź��ļ�: {file_path}, �ź���: {len(signals)}")

        # �ɹ���ȡ������ɾ���ļ�
        if signals:
            os.remove(file_path)
            print(f"��Ϣ - �ѳɹ�ɾ���ź��ļ�: {file_path}")
        else:
            # ����ļ��ǿյĻ�������Ч��Ҳɾ��������ֹ����
            os.remove(file_path)
            print(f"��Ϣ - �ź��ļ� {file_path} Ϊ�ջ���Ч����ɾ����")

    except Exception as e:
        print(f"���� - ��ȡ��ɾ���ź��ļ� '{file_path}' ʧ��: {e}")
        # ��������쳣�������ٴ�ɾ�����Է��Ƕ�ȡʱ�������ļ��Դ���
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"��Ϣ - ���쳣�����гɹ�ɾ���ļ�: {file_path}")
            except Exception as e_del:
                print(f"���� - �������쳣��ɾ���ļ� '{file_path}' �ٴ�ʧ��: {e_del}")

    return signals


# --- ���������ֲֹ�Ʊ����д���ļ��ĺ��� ---
def write_positions_to_file(positions, file_path):
    """
    ����ǰ�ֲֵĹ�Ʊ�����б�д��ָ���ļ���ÿ��һ����
    """
    try:
        # ��ȡ���гֲֹ�Ʊ�Ĵ���
        position_codes = list(positions.keys())
        if position_codes:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(position_codes))
            print(f"��Ϣ - �ѽ���ǰ {len(position_codes)} ���ֲֹ�Ʊ���������: {file_path}")
        else:
            # ���û�гֲ֣�������ļ�
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('')
            print(f"��Ϣ - ��ǰ�޳ֲ֣�������ļ�: {file_path}")
    except Exception as e:
        print(f"���� - д��ֲ��ļ� '{file_path}' ʧ��: {e}")
        
        

def standardize_stock_code(code):
    """�Զ�������Ʊ����ĺ�׺��SH��SZ"""
    if not isinstance(code, str) or len(code) < 6:
        return None
    base_code = code.split('.')[0]
    if base_code.startswith('6'):
        return f"{base_code}.SH"
    elif base_code.startswith('0') or base_code.startswith('3'):
        return f"{base_code}.SZ"
    return code
    
    
def format_stock_list_with_name(stock_list, context):
    """����Ʊ�����б���ʽ��Ϊ '����(����)' ���ַ�����ʽ"""
    if not stock_list:
        return "[]"
    
    formatted_items = []
    for code in stock_list:
        try:
            # ʹ��iQuant API��ȡ��Ʊ����
            name = context.get_stock_name(code)
            if not name:
                name = "δ֪"
            formatted_items.append(f"'{code}({name})'")
        except:
            formatted_items.append(f"'{code}(��ȡ����ʧ��)'")
            
    return "[" + ", ".join(formatted_items) + "]"

def init(context):
    """��ʼ������"""
    print("=== ���Գ�ʼ�� ===")
    context.set_account(account_id)
    print(f"���ý����˻�: {account_id}")


def handlebar(context):
    """K�ߴ�������"""
    if context.is_last_bar() == False:
        return
    
    global buy_stocks, sell_stocks, blacklist_stocks, priority_stocks, today_bought, allow_add_position, current_signals_detail
    
    print("\n--- ��ǰ�������� ---")
    # ע�⣺�˴���ӡ���Ǳ�����ʵ��ֵ���������� allow_add_position = True # ... False, ʵ����Ч���� True
    print(f"�Ƿ�����׷�Ӳ�λ: {allow_add_position}")
    print(f"���ֲֹ�Ʊ����: {max_position} ��")
    print(f"��������: {buy_type}")
    # �����������ͣ���ӡ��Ӧ�Ĳ���ֵ
    if buy_type == "fixed_amount":
        print(f"  - �̶�������: {fixed_buy_amount} Ԫ")
    elif buy_type == "total_fund_percent":
        print(f"  - �ʽ�ٷֱ�: {fund_percent * 100:.2f}% (���ʽ�)")
    elif buy_type == "available_fund_percent":
        print(f"  - �����ʽ�ٷֱ�: {available_fund_percent * 100:.2f}% (�����ʽ�)")
    elif buy_type == "lots_to_buy":
        print(f"  - Ĭ����������: {lots_to_buy} ��")
    print("--------------------")
    
    # ����K�����ڵ���ʱ�ź��б�
    current_buy_signals = []
    current_sell_signals = []

    # 1. �����ֶ��ź� (������ؿ���)
    if trade_test_switch == 1:
        current_buy_signals.extend(buy_stocks)
        current_sell_signals.extend(sell_stocks)

    # 2. ���ź��ļ����ؽ����ź�
    if signal_switch == 1:
        print("--- ����ź��ļ� ---")
        
        # 1. ���������ź�
        signals_from_file = read_and_delete_signal_file_new(signal_file)
        if signals_from_file:
            print(f"���ļ���ȡ���ź�: {len(signals_from_file)} ��")

            # �����ź�����
            newly_added_buy_signals = []
            newly_added_sell_signals = []
            already_processed_signals = []

            for signal in signals_from_file:
                signal_code = signal.get('code', '')
                signal_type = signal.get('type', 'buy')
                signal_level = signal.get('level', 'MBG')
                signal_reason = signal.get('reason', '��֪')
                signal_amount = signal.get('plan_amount', 0)

                std_signal = standardize_stock_code(signal_code)
                if not std_signal:
                    continue

                # ��ӡ�ź���ϸ��Ϣ
                print(f"  �ź�: {std_signal} [{signal_level}] {signal_type} - {signal_reason} (Ԥ��: {signal_amount}Ԫ)")

                # �洢�ź���ϸ��Ϣ
                current_signals_detail[std_signal] = {
                    'level': signal_level,
                    'reason': signal_reason,
                    'plan_amount': signal_amount,
                    'type': signal_type
                }

                # ����Ƿ��Ѵ���
                if std_signal in today_bought:
                    already_processed_signals.append(std_signal)
                else:
                    # ���ݷ��������ź�
                    if signal_type == 'buy':
                        newly_added_buy_signals.append(signal_code)
                    elif signal_type == 'sell':
                        newly_added_sell_signals.append(signal_code)
            
            # �����źż���������б���������������ӡ����
            if newly_added_buy_signals:
                print(f"���ļ���ȡ���������������ź�: {format_stock_list_with_name(newly_added_buy_signals, context)}")
                # ���������á�����������ʱ�ķ���
                print_buy_signal_details(newly_added_buy_signals, context)
                current_buy_signals.extend(newly_added_buy_signals)

            if newly_added_sell_signals:
                print(f"���ļ���ȡ���������������ź�: {format_stock_list_with_name(newly_added_sell_signals, context)}")
                current_sell_signals.extend(newly_added_sell_signals)

            if already_processed_signals:
                print(f"���ļ���ȡ���������Ѵ������źţ�������: {format_stock_list_with_name(already_processed_signals, context)}")
            
        print("--- �ź��ļ�������� ---")

    # 3. �źŴ�����ȥ��
    processed_buy_stocks = sorted(list(set([s for s in [standardize_stock_code(code) for code in current_buy_signals] if s])))
    processed_sell_stocks = sorted(list(set([s for s in [standardize_stock_code(code) for code in current_sell_signals] if s])))
    
    blacklist_stocks = [standardize_stock_code(s) for s in blacklist_stocks if standardize_stock_code(s)]
    priority_stocks = [standardize_stock_code(s) for s in priority_stocks if standardize_stock_code(s)]
        
    print("\n" + "="*20 + f" K�ߴ�����ʼ: {timetag_to_datetime(context.get_bar_timetag(context.barpos), '%Y-%m-%d %H:%M:%S')} " + "="*20)
    
    positions = get_account_detail().get("positions", {})
    write_positions_to_file(positions, current_positions_file)
    
    print(f"��ǰ�ֲ�����: {len(positions)} ����Ʊ")
    print("��ǰ�ֲֹ�Ʊ:", format_stock_list_with_name(list(positions.keys()), context))
    
    # 4. �����뽻�׾���
    if processed_buy_stocks or processed_sell_stocks:
        print(f"�ܴ������źų� (ȥ�غ�): {format_stock_list_with_name(processed_buy_stocks, context)}")
        print(f"�ܴ������źų� (ȥ�غ�): {format_stock_list_with_name(processed_sell_stocks, context)}")

        # 4.1 �����������յġ������б���
        # a. ���˺�����
        valid_buy_stocks = [s for s in processed_buy_stocks if s not in blacklist_stocks]
        if len(valid_buy_stocks) < len(processed_buy_stocks):
             print(f"������ʾ - ���Ƴ��������еĹ�Ʊ��")
        
        # b. ���˽����Ѵ�����
        temp_list = [s for s in valid_buy_stocks if s not in today_bought]
        if len(temp_list) < len(valid_buy_stocks):
            print(f"������ʾ - ���Ƴ������ѳ��Խ��׵Ĺ�Ʊ��")
        valid_buy_stocks = temp_list
        
        # c. �����ֹ�Ӳ֣������ѳֲֵ�
        if not allow_add_position:
            temp_list = [s for s in valid_buy_stocks if s not in positions]
            if len(temp_list) < len(valid_buy_stocks):
                stocks_filtered = [s for s in valid_buy_stocks if s in positions]
                print(f"������ʾ - ��ֹ׷�Ӳ�λ�����Ƴ��ֲ��еĹ�Ʊ: {format_stock_list_with_name(stocks_filtered, context)}")
            valid_buy_stocks = temp_list
        
        # 4.2 �����������յġ������б���
        valid_sell_stocks = [s for s in processed_sell_stocks if s in positions]
        if len(valid_sell_stocks) < len(processed_sell_stocks):
            print(f"������ʾ - �Ѵ������ź����Ƴ��ǳֲֵĹ�Ʊ��")

        # 5. ִ�н���
        if valid_buy_stocks:
            priority_buys = [s for s in valid_buy_stocks if s in priority_stocks]
            regular_buys = [s for s in valid_buy_stocks if s not in priority_stocks]
            sorted_buy_stocks = priority_buys + regular_buys
            
            print(f"���մ������б� (����͹��˺�): {format_stock_list_with_name(sorted_buy_stocks, context)}")
            handler_buy_trade(sorted_buy_stocks, context)
        else:
            print("����Ч�������Ʊ������ԭ���ں�����/�ѳֲ�/�����Ѵ���/����ֹ�Ӳ֣���")

        if valid_sell_stocks:
            print(f"���մ������б� (���˺�): {format_stock_list_with_name(valid_sell_stocks, context)}")
            handler_sale_trade(valid_sell_stocks, context)
        else:
            print("����Ч������Ʊ�����ڳֲ��У���")
    else:
        print("���н��׿��عرջ����κ��źţ���ִ�н��ס�")

    # ��ձ��δ��������ֶ��ź�
    buy_stocks = []
    sell_stocks = []
    
    

def handler_buy_trade(symbols, context):
    """�������뽻�� (���Ż��Ͳ�ȫ�߼�)"""
    
    import datetime  # ����������datetimeģ����ʹ��now()
    import time      # ����������timeģ����ʹ��sleep()
    
    now = datetime.datetime.now()
    current_time_str = now.strftime('%H%M')
    current_time = int(current_time_str)

    is_trading_hours = (930 <= current_time <= 1130) or (1300 <= current_time <= 1500)
    is_post_market_hours = (1505 <= current_time <= 1530)
    
    if not is_trading_hours and not is_post_market_hours:
        print(f"���� - ��ǰϵͳʱ��({current_time_str})�����κν���ʱ���ڣ������������������")
        return

    global today_bought, max_position, disallowed_name_substrings
    
    # ѭ����ʼǰ��ȡһ�Σ������ظ���ѯ
    detail = get_account_detail()
    open_orders = detail.get("orders", {})

    if not detail.get("accouts"):
        print("�����޷���ȡ�˻��ʽ���Ϣ���������롣")
        return

    for symbol in symbols:
        # ʵʱ��ȡ���µĳֲ�����
        current_positions = get_account_detail().get("positions", {})
        is_add_position = symbol in current_positions
        
        # ����λ����Ż���ֻ���ڡ��¿��֡�ʱ�ż�����ֲ�����
        if not is_add_position and len(current_positions) >= max_position:
            print(f"��ʾ - �ֲ�����({len(current_positions)}/{max_position})���޷��¿��֡�����: {format_stock_list_with_name([symbol], context)}")
            today_bought[symbol] = {"status": "skipped", "reason": "�ֲ�����"}
            continue
            
        is_gem_or_star = symbol.startswith('30') or symbol.startswith('688')
        if not is_trading_hours and not (is_post_market_hours and is_gem_or_star):
            print(f"��ʾ - ��Ʊ {symbol} ��ǰʱ��({current_time_str})��������Ч����ʱ���ڣ�������")
            continue

        # --- ���й����߼� ---
        try:
            stock_name = context.get_stock_name(symbol)
            if any(keyword in (stock_name or "") for keyword in disallowed_name_substrings):
                print(f"����ʧ�� - ��Ʊ: {format_stock_list_with_name([symbol], context)}, ԭ��: ���ƺ����ôʡ�")
                today_bought[symbol] = {"status": "skipped", "reason": "���ƺ����ô�"}
                continue
        except Exception as e:
            print(f"���� - ��ȡ��Ʊ {symbol} ����ʧ��: {e}")

        prefix = '688' if symbol.startswith('688') else symbol.split('.')[0][:2]
        if prefix not in allowed_stock_types:
            print(f"����ʧ�� - ��Ʊ: {format_stock_list_with_name([symbol], context)}, ԭ��: ����({prefix})��������")
            today_bought[symbol] = {"status": "failed", "reason": "���Ͳ�����"}
            continue

        current_price, change_percent = get_current_price(symbol, context)
        if current_price <= 0:
            print(f"����ʧ�� - ��Ʊ: {format_stock_list_with_name([symbol], context)}, ԭ��: ��ȡ�۸�ʧ�ܡ�")
            today_bought[symbol] = {"status": "failed", "reason": "�۸���Ч"}
            continue
        
        if (is_gem_or_star and change_percent > 19.9) or (not is_gem_or_star and change_percent > 9.9):
            print(f"����ʧ�� - ��Ʊ: {format_stock_list_with_name([symbol], context)}, ԭ��: ����ͣ��")
            today_bought[symbol] = {"status": "skipped", "reason": "����ͣ"}
            continue
        
        # --- �������޸ġ������º���������� ---
        # ��ȡ�ź���ϸ��Ϣ
        signal_detail = current_signals_detail.get(symbol, {})
        signal_level = signal_detail.get('level', 'δ֪')
        signal_reason = signal_detail.get('reason', 'δ֪')
        signal_amount = signal_detail.get('plan_amount', 0)

        print(f"\n>>> ׼������{'׷��' if is_add_position else '����'}[{signal_level}]�ź�: {format_stock_list_with_name([symbol], context)} (ģʽ: {buy_type}) <<<")
        print(f"    - �ź���Ϣ: {signal_reason}")
        print(f"    - AI�Ƽ��: {signal_amount}Ԫ")
        lots = calculate_buy_lots(symbol, current_price, context, detail)
        print(f"    - �����������: {lots} ��")

        if lots <= 0:
            print(f"����ʧ�� - ��Ʊ: {format_stock_list_with_name([symbol], context)}, ԭ��: �����������Ϊ0��")
            today_bought[symbol] = {"status": "skipped", "reason": "�������Ϊ0"}
            continue
        
        # --- ���ȳ������ж��� ---
        if symbol in open_orders:
            existing_order_id = open_orders.get(symbol)
            print(f"��ʾ - ���ֹ�Ʊ {format_stock_list_with_name([symbol], context)} ����δ�ɽ����� (ID: {existing_order_id})���������ȳ���...")
            try:
                if can_cancel_order(str(existing_order_id), account_id, "STOCK"):
                    cancel(str(existing_order_id), account_id, "STOCK", context)
                    print(f"   -> ���� {existing_order_id} �ѷ��ͳ������󣬵ȴ�2����ȷ���ʽ��ͷ�...")
                    time.sleep(2)
                    print(f"   -> �����ȴ���ɣ������µ����̡�")
                else:
                    print(f"   -> ���� {existing_order_id} ���ɳ������Ѵ�����")
            except Exception as e:
                print(f"   -> ���������쳣: {e}�������µ����̡�")
        
        # --- �µ�ǰ�����ʽ��� (ʵʱ��ȡ) ---
        current_detail_before_order = get_account_detail()
        available_cash = current_detail_before_order.get("accouts", {}).get("Available_money", 0)
        amount_needed = current_price * lots

        if amount_needed > available_cash:
            print(f"����ʧ�� - ��Ʊ: {format_stock_list_with_name([symbol], context)}, ԭ��: �ʽ���, ��Ҫ {amount_needed:.2f}, ���� {available_cash:.2f}")
            today_bought[symbol] = {"status": "failed", "reason": "�ʽ���"}
            continue
            
        print(f"׼���µ�{'׷��' if is_add_position else '����'} - ��Ʊ: {format_stock_list_with_name([symbol], context)}, ����: {lots}, Ԥ�ƽ��: {amount_needed:.2f} Ԫ")
        print(f"   -> ��ǰ�۸�: {current_price:.2f}, �����ʽ�: {available_cash:.2f}")
        previous_volume = get_vol(symbol)
        print(f"   -> �µ�ǰ�ֲ���: {previous_volume}")
        
        order_placed = False
        # --- ���Զ��ּۺ��м��µ� ---
        for price_type, price_type_name in [(14, "���ּ�"), (12, "�м�")]:
            print(f"-> ����ʹ�� [{price_type_name}] {'׷��' if is_add_position else '����'}...")

            try:
                print(f"   -> ׼���������: opType=23, orderType=1101, account={account_id}, symbol={symbol}, prType={price_type}, price=0.0, volume={int(lots)}")
                order_id = passorder(23, 1101, account_id, symbol, price_type, 0.0, int(lots), "", 1, "", context)
                print(f"   -> �µ�����ID: {order_id}")

                if not order_id or str(order_id) == "0" or str(order_id) == "" or order_id == 0:
                    print(f"   -> �µ����󱻹�̨�ܾ� (order_id: {order_id})��������һ�ּ۸���ԡ�")
                    continue
            except Exception as e:
                print(f"   -> �µ��쳣: {e}��������һ�ּ۸���ԡ�")
                continue

            time.sleep(3) 

            # ����Ƿ�ɽ�
            current_vol = get_vol(symbol)
            if current_vol > previous_volume:
                print(f"��{'׷��' if is_add_position else '����'}�ɹ��� - ��Ʊ: {format_stock_list_with_name([symbol], context)}, ����: {lots}, ʹ�ü۸����: [{price_type_name}]")
                today_bought[symbol] = {"status": "success"}
                order_placed = True
                break
            else:
                print(f"-> [{price_type_name}] {'׷��' if is_add_position else '����'}����ʧ�ܻ�δ�����ɽ���")
                try:
                    order_id_str = str(order_id)
                    if can_cancel_order(order_id_str, account_id, "STOCK"):
                        print(f"   ����δ�ɽ����� - ��Ʊ: {format_stock_list_with_name([symbol], context)}, ����ID: {order_id}")
                        cancel(order_id_str, account_id, "STOCK", context)
                        time.sleep(1)
                    else:
                        print(f"   -> ���� {order_id} ���ɳ������Ѵ�����")
                except Exception as e:
                    print(f"   -> �����쳣: {e}")
        
        if not order_placed:
            print(f"��{'׷��' if is_add_position else '����'}ʧ�ܡ� - ��Ʊ: {format_stock_list_with_name([symbol], context)}, ԭ��: ���м۸���Ծ�����ʧ�ܡ�")
            today_bought[symbol] = {"status": "failed", "reason": "���м۸���Ծ�ʧ��"}
            

def get_account_detail():
    """��ȡ�˻�����"""
    detail = {}
    positions = {}
    accouts = {}
    orders = {}
    
    try:
        position_data = get_trade_detail_data(account_id, "STOCK", "POSITION")
        for obj in position_data:
            symbol = standardize_stock_code(obj.m_strInstrumentID)
            if symbol and (int(obj.m_nVolume) > 0):
                positions[symbol] = {
                    "vol": obj.m_nVolume,
                    "valid_vol": obj.m_nCanUseVolume,
                    "float_profit": getattr(obj, "m_dFloatProfit", 0) 
                }
        
        accout_data = get_trade_detail_data(account_id, "STOCK", "ACCOUNT")
        for obj in accout_data:
            accouts["Available_money"] = obj.m_dAvailable
            accouts["all_money"] = obj.m_dBalance
        
        orders_data = get_trade_detail_data(account_id, "STOCK", "ORDER")
        for obj in orders_data:
            if obj.m_nVolumeTotalOriginal != obj.m_nVolumeTraded:
                symbol = standardize_stock_code(obj.m_strInstrumentID)
                if symbol:
                    orders[symbol] = obj.m_strOrderSysID
    except Exception as e:
        print(f"��ȡ�˻�����ʱ��������: {e}")
            
    detail["positions"] = positions
    detail["accouts"] = accouts
    detail["orders"] = orders
    return detail

def deal_callback(context, dealInfo):
    """�ɽ�״̬�仯�ص�"""
    print("\n--- �ɽ��ص�(deal_callback)���� ---")
    if hasattr(dealInfo, "m_strInstrumentID") and hasattr(dealInfo, "m_nVolume"):
        symbol = dealInfo.m_strInstrumentID
        volume = dealInfo.m_nVolume
        offset_flag = dealInfo.m_nOffsetFlag 
        direction = "����" if offset_flag == 48 else "����" if offset_flag == 49 else f"δ֪����(OffsetFlag:{offset_flag})"
        price = getattr(dealInfo, "m_dPrice", 0.0)
        print(f"�����׻ر��� ����: {direction}, ��Ʊ: {symbol}, ����: {volume}��, �ɽ��۸�: {price:.2f}")
    print("-----------------------------------\n")

def orderError_callback(context, passOrderInfo, msg):
    """�µ������ص�����"""
    print("\n--- �µ������ص�(orderError_callback)���� ---")
    order_code = getattr(passOrderInfo, "orderCode", "δ֪����")
    symbol = getattr(passOrderInfo, "instrument", "δ֪��Ʊ")
    print(f"���µ�ʧ�ܡ� - ��Ʊ: {symbol}, ��������: {order_code}, ������������Ϣ: {msg}")
    global today_bought
    if symbol != "δ֪��Ʊ":
        today_bought[symbol] = {"status": "failed", "reason": msg}
    print("------------------------------------------\n")

def handler_sale_trade(symbols, context):
    """������������ (���Ż���)"""
    # �޸���ʹ�õ�ǰϵͳʱ�������K��ʱ��
    import datetime
    now = datetime.datetime.now()
    current_time_str = now.strftime('%H%M')
    current_time = int(current_time_str)

    # ͬʱ���K��ʱ�䣨���ڲο���
    bar_time_str = timetag_to_datetime(context.get_bar_timetag(context.barpos), '%H%M')
    bar_time = int(bar_time_str)

    print(f"ʱ���� - ��ǰϵͳʱ��: {current_time_str}, K��ʱ��: {bar_time_str}")

    if not ((930 <= current_time <= 1130) or (1300 <= current_time <= 1500)):
        print(f"���� - ��ǰϵͳʱ��({current_time_str})���ڽ���ʱ���ڣ�������������������")
        print("A�ɽ���ʱ��: 9:30-11:30, 13:00-15:00")
        return

    open_orders = get_account_detail().get("orders", {})

    for symbol in symbols:
        position = get_account_detail().get("positions", {}).get(symbol)
        
        if not position or position["valid_vol"] <= 0:
            print(f"����ʧ�� - ��Ʊ: {format_stock_list_with_name([symbol], context)}, ԭ��: �޿��óֲ�")
            continue

        sell_vol = int(position["valid_vol"] / 100) * 100
        if sell_vol <= 0:
            print(f"����ʧ�� - ��Ʊ: {format_stock_list_with_name([symbol], context)}, ԭ��: ���óֲֲ���1��({position['valid_vol']}��)")
            continue

        if symbol in open_orders:
            existing_order_id = open_orders[symbol]
            print(f"��ʾ - ���ֹ�Ʊ {format_stock_list_with_name([symbol], context)} ����δ�ɽ����� (ID: {existing_order_id})�����ȳ���...")
            if can_cancel_order(str(existing_order_id), account_id, "STOCK"):
                cancel(str(existing_order_id), account_id, "STOCK", context)
                print(f"   -> ���� {existing_order_id} �ѷ��ͳ������󣬵ȴ�2��...")
                import time
                time.sleep(2) # ͳһ�ȴ�ʱ��

        print(f"׼���µ����� - ��Ʊ: {format_stock_list_with_name([symbol], context)}, ����: {sell_vol}��")
        previous_volume = position["vol"]
        print(f"   -> ����ǰ�ֲ���: {previous_volume}")

        # ����˻�״̬
        account_detail = get_account_detail()
        available_cash = account_detail.get("accouts", {}).get("Available_money", 0)
        print(f"   -> ��ǰ�����ʽ�: {available_cash:.2f}")

        # ���ֲ��Ƿ��㹻
        if previous_volume < sell_vol:
            print(f"������ʧ�ܡ� - ��Ʊ: {format_stock_list_with_name([symbol], context)}, ԭ��: �ֲֲ���, ��ǰ�ֲ�: {previous_volume}, ��������: {sell_vol}")
            continue

        # ʹ�ö��ּ�����
        try:
            # ����API�ĵ��޸�passorder����
            # passorder(opType, orderType, accountid, orderCode, prType, modelprice, volume[, strategyName, quickTrade, userOrderId], ContextInfo)
            # opType: 24=����, orderType: 1101=���ɵ��˺���ͨ��/�ַ�ʽ, prType: 14=���ּ�
            print(f"   -> ׼����������: opType=24, orderType=1101, account={account_id}, symbol={symbol}, prType=14, price=0.0, volume={int(sell_vol)}")

            order_id = passorder(24, 1101, account_id, symbol, 14, 0.0, int(sell_vol), "", 1, "", context)
            print(f"   -> �����µ�����ID: {order_id}")

            if not order_id or str(order_id) == "0" or str(order_id) == "" or order_id == 0:
                print(f"������ʧ�ܡ� - ��Ʊ: {format_stock_list_with_name([symbol], context)}, ԭ��: �µ����󱻹�̨�ܾ� (order_id: {order_id})")
                continue

        except Exception as e:
            print(f"������ʧ�ܡ� - ��Ʊ: {format_stock_list_with_name([symbol], context)}, ԭ��: �µ��쳣: {e}")
            continue

        import time
        time.sleep(3) # ���ٵȴ�ʱ�䵽3��

        current_vol = get_vol(symbol)
        if current_vol < previous_volume:
             print(f"�������ɹ��� - ��Ʊ: {format_stock_list_with_name([symbol], context)}, ����: {sell_vol}��")
        else:
             print(f"������ʧ�ܡ� - ��Ʊ: {format_stock_list_with_name([symbol], context)}, ԭ��: �µ���ֲ�δ�仯��")

             # ���Գ���δ�ɽ�����
             try:
                 if can_cancel_order(str(order_id), account_id, "STOCK"):
                    print(f"   ����δ�ɽ����� - ��Ʊ: {format_stock_list_with_name([symbol], context)}, ����ID: {order_id}")
                    cancel(str(order_id), account_id, "STOCK", context)
                 else:
                    print(f"   -> ���� {order_id} ���ɳ������Ѵ�����")
             except Exception as e:
                 print(f"   -> �����쳣: {e}")
                

def get_vol(symbol):
    """��ȡָ����Ʊ�ĵ�ǰ�ֲ���(��)��"""
    detail = get_account_detail()
    return detail.get("positions", {}).get(symbol, {}).get("vol", 0)

def get_current_price(stock, context):
    """��ȡ��ǰ�۸�ͽ����ǵ���"""
    try:
        df = context.get_market_data(['close'], [stock], count=2, period='1d', dividend_type='front', skip_paused=True)
        if df is not None and not df.empty and 'close' in df.columns and len(df['close']) > 0:
            current_price = float(df['close'].iloc[-1])
            
            # �����ǵ���
            change_percent = 0.0
            if len(df['close']) > 1:
                prev_close = float(df['close'].iloc[-2])
                if prev_close > 0:
                    change_percent = (current_price / prev_close - 1) * 100
            
            if 0 < current_price < 10000:
                return current_price, change_percent # ���ؼ۸���ǵ���
            else:
                 print(f"����: ��ȡ����Ʊ {stock} �ļ۸��쳣: {current_price}")
                 return 0, 0
        else:
            print(f"����: ��ȡ��Ʊ {stock} ��������ʧ�ܣ�����ͣ�ƻ�����Ϊ�ա�")
            return 0, 0
    except Exception as e:
        print(f"��ȡ�۸�ʱ�����쳣 - ��Ʊ: {stock}, ����: {e}")
        return 0, 0































